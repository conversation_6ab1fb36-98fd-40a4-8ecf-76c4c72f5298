import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'font_weight_helper.dart';


class Styles {
  static  TextStyle font22BlackBold = TextStyle(
    fontSize: 22.sp,
    fontWeight: FontWeightHelper.bold,
    color: Colors.black,
    fontFamily: 'PlayfairDisplay',

  );
  static TextStyle font18BlackBold = TextStyle(
    fontSize: 18.sp,
    fontWeight: FontWeightHelper.bold,
    color: Colors.black,
    fontFamily: 'PlayfairDisplay',
  );
  static TextStyle font16BlackMedium =TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeightHelper.medium,
    color: Colors.black,
    fontFamily: 'WorkSans',
  );
  static TextStyle font16BlackBold =TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeightHelper.bold,
    color: Colors.black,
    fontFamily: 'WorkSans',
  );
  static TextStyle font24BlackBold =TextStyle(
    fontSize: 24.sp,
    fontWeight: FontWeightHelper.bold,
    color: Colors.black,
    fontFamily: 'PlayfairDisplay',
  );

 }