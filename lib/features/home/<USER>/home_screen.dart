import 'package:animated_notch_bottom_bar/animated_notch_bottom_bar/animated_notch_bottom_bar.dart';
import 'package:bagzz/core/helpers/extentions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:bagzz/core/helpers/spacing.dart';

import '../../../core/theming/styles.dart';
import 'home_page.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final NotchBottomBarController _notchBottomBarController =
      NotchBottomBarController(index: 0);
  final PageController _pageController = PageController(initialPage: 0);

  List<Widget> pages = [
    HomePage(),
    Container(child: Text('Search Page')),
    Container(child: Text('Cart Page')),
    //  SearchPage(),
    //  CartPage(),
    //  favoritePage(),
  ];
  int currentIndex = 0;

  @override
  void dispose() {
    _notchBottomBarController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  void onTap(int index) {
    setState(() {
      currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,

      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Row(
          children: [
            GestureDetector(
              onTap: () {},
              child: SvgPicture.asset('assets/svgs/menu.svg'),
            ),
            Spacing.horizontalSpace(10),
            Text(
              'bagzz',
              style: Styles.font22BlackBold.copyWith(
                fontFamily: 'PlayfairDisplay',
              ),
            ),
            Spacer(),
            CircleAvatar(
              radius: 20.r,
              backgroundImage: AssetImage('assets/images/img1.png'),
            ),
          ],
        ),
      ),

      body: SafeArea(
        child: PageView(
          physics: NeverScrollableScrollPhysics(),
          controller: _pageController,
          onPageChanged: (value) {
            setState(() {
              currentIndex = value;
            });
          },
          children: pages,
        ),
      ),

      bottomNavigationBar: Padding(
        padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 20.w),
        child: Container(
          height: 50.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
              bottomLeft: Radius.circular(20.r),
              bottomRight: Radius.circular(20.r),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 1,
                blurRadius: 7,
                offset: Offset(0, 3), // changes position of shadow
              ),
            ],
          ),
          child: Row(
            children: [
              // home
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    onTap(0);
                    _pageController.jumpToPage(0);
                  },
                  child: SvgPicture.asset('assets/svgs/home.svg',height: 20.h,),
                ),
              ),
              // search
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    onTap(1);
                    _pageController.jumpToPage(1);
                  },
                  child: SvgPicture.asset('assets/svgs/search.svg',height: 20.h,),
                ),
              ),
              // cart
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    onTap(3);
                    _pageController.jumpToPage(3);
                  },
                  child: SvgPicture.asset('assets/svgs/fav.svg',height: 20.h,),
                ),
              ),
              // favorite
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    onTap(2);
                    _pageController.jumpToPage(2);
                  },
                  child: SvgPicture.asset('assets/svgs/cart.svg',height: 20.h,),
                ),
              ),

            ],
          ),
        ),
      ),
    );
  }
}

//bottomNavigationBar:  AnimatedNotchBottomBar(
//   pageController: _pageController,
//   bottomBarItems: [
//     const BottomBarItem(
//         inActiveItem: Icon(
//            Icons.home_filled,
//             color: Colors.blueGrey,
//         ),
//         activeItem: Icon(
//           Icons.home_filled,
//           color: Colors.blueAccent,
//         ),
//         itemLabel: 'Page 1',
//   ),
//     const BottomBarItem(
//         inActiveItem: Icon(
//             Icons.star,
//             color: Colors.blueGrey,
//           ),
//         activeItem: Icon(
//             Icons.star,
//             color: Colors.blueAccent,
//         ),
//         itemLabel: 'Page 2',
//   ),
//
//      ///svg item
//     BottomBarItem(
//         inActiveItem: SvgPicture.asset(
//           'assets/search_icon.svg',
//            color: Colors.blueGrey,
//        ),
//        activeItem: SvgPicture.asset(
//           'assets/search_icon.svg',
//            color: Colors.black,
//        ),
//        itemLabel: 'Page 3',
//     ),
//      ...
// )
