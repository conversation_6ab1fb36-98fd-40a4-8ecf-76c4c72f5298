import 'package:bagzz/core/helpers/extentions.dart';
import 'package:bagzz/core/helpers/spacing.dart';
import 'package:bagzz/features/home/<USER>/widgets/button_text.dart';
import 'package:bagzz/features/home/<USER>/widgets/categorie_cart.dart';
import 'package:bagzz/features/home/<USER>/widgets/custom_grid_view.dart';
import 'package:bagzz/features/home/<USER>/widgets/item_cart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theming/styles.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // advertisement
  List<String> advertisement = [
    'assets/images/img.png',
    'assets/images/img1.png',
  ];
  int currentAdvertisementIndex = 0;

  // products
  List<Map<String, String>> products = [
    {
      'name': 'Artsy',
      'img': 'assets/images/bag1.png',
    },
    {
      'name': 'Berkely',
      'img': 'assets/images/bag2.png',
    },
    {
      'name': 'Capucinus',
      'img': 'assets/images/bag3.png',
    },
    {
      'name': 'Monogram',
      'img': 'assets/images/bag4.png',
    },
  ];
  // categories
  List<Map<String, String>> categories = [
    {
      'name': 'Handle bags',
      'img': 'assets/images/categories1.png',
    },
    {
      'name': 'Crossbody bags',
      'img': 'assets/images/categories2.png',
    },
    {
      'name': 'Shoulder bags',
      'img': 'assets/images/categories3.png',
    },
    {
      'name': 'Tote bag',
      'img': 'assets/images/categories4.png',
    },

  ];



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // advertisement
              Stack(
                children: [
                  // advertisement image
                  GestureDetector(
                    onTap: () {
                      context.pushNamed('product');
                    },
                    child: Container(
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: Image.asset(
                        fit: BoxFit.fill,
                        advertisement[currentAdvertisementIndex],
                      ),
                    ),
                  ),
                  // advertisement dots
                  Positioned(
                    bottom: 0,
                    right: 22.w,
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          currentAdvertisementIndex =
                              (currentAdvertisementIndex + 1) %
                              advertisement.length;
                        });
                      },
                      child: Container(
                        height: 50.h,
                        width: 50.w,
                        color: Colors.black,
                        child: Icon(Icons.arrow_forward_ios, color: Colors.white),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 75.w,
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          currentAdvertisementIndex =
                              (currentAdvertisementIndex - 1) %
                              advertisement.length;
                        });
                      },
                      child: Container(
                        height: 50.h,
                        width: 50.w,
                        color: Colors.black,
                        child: Icon(Icons.arrow_back_ios, color: Colors.white),
                      ),
                    ),
                  ),


                  Positioned(
                    right: 30.w,
                    top: 50.w,
                    child:Column(
                      // mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                        Container(
                          color: Colors.white,
                          child:Text('This ',style: Styles.font22BlackBold.copyWith(fontFamily: 'PlayfairDisplay'),),
                        ),
                        Container(
                          color: Colors.white,
                          child:Text('season’s',style: Styles.font22BlackBold.copyWith(fontFamily: 'PlayfairDisplay'),),
                        ),
                        Container(
                          color: Colors.white,
                          child:Text('latest',style: Styles.font22BlackBold.copyWith(fontFamily: 'PlayfairDisplay'),),
                        ),
                      ]
                    ) ,
                  ),
                ],
              ),
              Spacing.verticalSpace(20),

              // products view
              CustomGridView(

                widget: ItemCart(
                  title: products [0]['name'] ,
                  image: products [0]['img'] ,
                ),
                widgetLength: products.length,
              ),
              GridView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 10.h,
                  crossAxisSpacing: 10.w,
                  childAspectRatio: 0.7,
                ),
                itemCount: products.length,
                itemBuilder: (context, index) {
                  return ItemCart(
                    title: products [index]['name'] ,
                    image: products [index]['img'] ,
                  );
                },
              ),

              Spacing.verticalSpace(20),
              // check all latest
              Center(child: ButtonText()),

              Spacing.verticalSpace(20),
              // categories

              Text('Shop by categories', style: Styles.font24BlackBold),
              Spacing.verticalSpace(10),

              GridView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 10.h,
                  crossAxisSpacing: 10.w,
                  childAspectRatio: 0.7,
                ),
                itemCount: products.length,
                itemBuilder: (context, index) {
                  return CategorieCart(
                    title: categories [index]['name'] ,
                    image: categories [index]['img'] ,
                  );
                },
              ),
             ],
          ),
        ),
      ),
    );
  }
}
