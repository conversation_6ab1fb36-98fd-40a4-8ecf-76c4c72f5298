import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/helpers/spacing.dart';
import '../../../../core/theming/styles.dart';

class ItemCart extends StatelessWidget {
  const ItemCart({super.key,
   this.title,
    // required this.description,
     this.image,
    // required this.price,
  });

  final String? title ;
  // final String description = 'Shop now';
  final String? image ;
  // final String price = '\$120';


  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: 170.w,
          height: 210.h,
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(.15),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Column(
            children: [
              Spacing.verticalSpace(6),
              Image.asset(image ??'assets/images/bag1.png', height: 110.h),
              Spacing.verticalSpace(10),
              Text(title ??'Artsy', style: Styles.font18BlackBold),
              Spacing.verticalSpace(10),
              Text('Shop now', style: Styles.font18BlackBold),
              Spacing.verticalSpace(6),
              Container(
                width: 85.w,
                height: 3.h,
                decoration: BoxDecoration(
                  color: Colors.black,
                  // borderRadius: BorderRadius.circular(10.r),
                ),
              ),
            ],
          ),
        ),

        Positioned(
          right: 10.w,
          top: 10.h,
          child: SvgPicture.asset('assets/svgs/fav.svg', height: 20.h),
        ),
      ],
    );
  }
}
