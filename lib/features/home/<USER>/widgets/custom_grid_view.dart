import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'item_cart.dart';

class CustomGridView extends StatelessWidget {
  final List<Map<String, dynamic>> products;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double childAspectRatio;
  final NullableIndexedWidgetBuilder? itemBuilder; // Custom item builder

  const CustomGridView({
    super.key,
    required this.products,
    this.crossAxisCount = 2,
    this.mainAxisSpacing = 10.0,
    this.crossAxisSpacing = 10.0,
    this.childAspectRatio = 0.7,
    this.itemBuilder, // Optional custom builder
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: mainAxisSpacing.h,
        crossAxisSpacing: crossAxisSpacing.w,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: products.length,
      itemBuilder: itemBuilder ?? (context, index) {
        return ItemCart(
          title: products[index]['name'],
          image: products[index]['img'],
        );
      },
    );
  }
}