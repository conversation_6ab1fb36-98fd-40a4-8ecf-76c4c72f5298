import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theming/styles.dart';

class CategorieCart extends StatelessWidget {
  const CategorieCart({super.key, this.image, this.title});

  final String? image ;
  final String? title ;


  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(.15),
            // borderRadius: BorderRadius.circular(10.r),
          ),
          child: Image.asset(image ??'assets/images/categories1.png', height: 225.h,width: 170.w,
          fit: BoxFit.fill,
          ),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            child: Text(title ?? 'Handle bags' ,style: Styles.font16BlackBold,),

        ),),
      ],
    );
  }
}
