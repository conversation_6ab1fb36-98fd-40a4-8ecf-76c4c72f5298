import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theming/styles.dart';

class ButtonText extends StatelessWidget {
  const ButtonText({super.key, this.text});

  final String? text ;

  @override
  Widget build(BuildContext context) {
    return IntrinsicWidth(
      child: Container(
        // height: 35.h,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
            border: Border.all(
              color: Colors.black,
              width: 2.w,
            )
        ),
        child: Center(
          child: Text(text ?? 'Check all latest',style: Styles.font16BlackMedium,),
        ),
      ),
    );
  }
}
